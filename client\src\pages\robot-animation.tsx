import React, { useEffect, useRef, useState } from 'react';
import { useLocation } from 'wouter';
import { ArrowLeft, Home, Move, RotateCcw, Maximize2, Minimize2 } from 'lucide-react';
import { Button } from '@/components/ui/button';

const RobotAnimation: React.FC = () => {
  const [, setLocation] = useLocation();
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // State for moveable and scalable iframe
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [scale, setScale] = useState(1);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [showRobotAnimation, setShowRobotAnimation] = useState(true);

  // Handle navigation back to home
  const handleGoHome = () => {
    setLocation('/');
  };

  // Handle going back
  const handleGoBack = () => {
    window.history.back();
  };

  // Handle mouse down for dragging
  const handleMouseDown = (e: React.MouseEvent) => {
    if (isFullscreen) return;
    setIsDragging(true);
    setDragStart({
      x: e.clientX - position.x,
      y: e.clientY - position.y
    });
  };

  // Handle mouse move for dragging
  const handleMouseMove = (e: MouseEvent) => {
    if (!isDragging || isFullscreen) return;
    setPosition({
      x: e.clientX - dragStart.x,
      y: e.clientY - dragStart.y
    });
  };

  // Handle mouse up to stop dragging
  const handleMouseUp = () => {
    setIsDragging(false);
  };

  // Handle scale change
  const handleScaleChange = (newScale: number) => {
    setScale(Math.max(0.3, Math.min(2, newScale)));
  };

  // Handle reset position and scale
  const handleReset = () => {
    setPosition({ x: 0, y: 0 });
    setScale(1);
    setIsFullscreen(false);
  };

  // Handle fullscreen toggle
  const handleFullscreenToggle = () => {
    setIsFullscreen(!isFullscreen);
    if (!isFullscreen) {
      setPosition({ x: 0, y: 0 });
      setScale(1);
    }
  };

  // Toggle between robot animation and nested daswos app
  const handleToggleContent = () => {
    setShowRobotAnimation(!showRobotAnimation);
  };

  useEffect(() => {
    // Set page title
    document.title = 'Daswos Robot Animation';

    // Add mouse event listeners for dragging
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);

    // Add keyboard shortcuts
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'r' && (e.ctrlKey || e.metaKey)) {
        e.preventDefault();
        handleReset();
      } else if (e.key === 'f' && (e.ctrlKey || e.metaKey)) {
        e.preventDefault();
        handleFullscreenToggle();
      } else if (e.key === 't' && (e.ctrlKey || e.metaKey)) {
        e.preventDefault();
        handleToggleContent();
      } else if (e.key === '=' && (e.ctrlKey || e.metaKey)) {
        e.preventDefault();
        handleScaleChange(scale + 0.1);
      } else if (e.key === '-' && (e.ctrlKey || e.metaKey)) {
        e.preventDefault();
        handleScaleChange(scale - 0.1);
      }
    };

    document.addEventListener('keydown', handleKeyDown);

    // Cleanup on unmount
    return () => {
      document.title = 'Daswos';
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [isDragging, dragStart, scale]);

  return (
    <div className="min-h-screen bg-[#E0E0E0] dark:bg-[#222222] flex flex-col">
      {/* Header with navigation */}
      <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-4 py-3 flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <Button
            onClick={handleGoBack}
            variant="outline"
            size="sm"
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Back
          </Button>
          <Button
            onClick={handleGoHome}
            variant="outline"
            size="sm"
            className="flex items-center gap-2"
          >
            <Home className="h-4 w-4" />
            Home
          </Button>
        </div>
        <h1 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
          {showRobotAnimation ? 'Daswos Robot Animation' : 'Nested Daswos App'}
        </h1>
        <div className="flex items-center space-x-2">
          <Button
            onClick={handleToggleContent}
            variant="outline"
            size="sm"
            className="flex items-center gap-2"
          >
            {showRobotAnimation ? '🤖' : '🏠'}
            {showRobotAnimation ? 'Show App' : 'Show Robot'}
          </Button>
        </div>
      </div>

      {/* Control Panel */}
      <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-4 py-2">
        <div className="flex items-center justify-center space-x-4">
          <div className="flex items-center space-x-2">
            <label className="text-sm font-medium text-gray-700 dark:text-gray-300">Scale:</label>
            <input
              type="range"
              min="0.3"
              max="2"
              step="0.1"
              value={scale}
              onChange={(e) => handleScaleChange(parseFloat(e.target.value))}
              className="w-24"
            />
            <span className="text-sm text-gray-600 dark:text-gray-400 min-w-[3rem]">
              {Math.round(scale * 100)}%
            </span>
          </div>

          <Button
            onClick={handleReset}
            variant="outline"
            size="sm"
            className="flex items-center gap-2"
          >
            <RotateCcw className="h-4 w-4" />
            Reset
          </Button>

          <Button
            onClick={handleFullscreenToggle}
            variant="outline"
            size="sm"
            className="flex items-center gap-2"
          >
            {isFullscreen ? <Minimize2 className="h-4 w-4" /> : <Maximize2 className="h-4 w-4" />}
            {isFullscreen ? 'Minimize' : 'Fullscreen'}
          </Button>
        </div>
      </div>

      {/* Main Content Area */}
      <div className="flex-1 relative overflow-hidden">
        {/* Moveable and Scalable Iframe Container */}
        <div
          ref={containerRef}
          className={`absolute bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-300 dark:border-gray-600 ${
            isFullscreen ? 'inset-0 rounded-none' : ''
          } ${isDragging ? 'cursor-grabbing' : 'cursor-grab'}`}
          style={{
            transform: isFullscreen
              ? 'none'
              : `translate(${position.x}px, ${position.y}px) scale(${scale})`,
            transformOrigin: 'top left',
            width: isFullscreen ? '100%' : '800px',
            height: isFullscreen ? '100%' : '600px',
            left: isFullscreen ? '0' : '50%',
            top: isFullscreen ? '0' : '50%',
            marginLeft: isFullscreen ? '0' : '-400px',
            marginTop: isFullscreen ? '0' : '-300px',
            zIndex: isDragging ? 1000 : 1,
          }}
        >
          {/* Drag Handle */}
          {!isFullscreen && (
            <div
              className="bg-gray-100 dark:bg-gray-700 px-4 py-2 border-b border-gray-300 dark:border-gray-600 flex items-center justify-between cursor-grab active:cursor-grabbing"
              onMouseDown={handleMouseDown}
            >
              <div className="flex items-center space-x-2">
                <Move className="h-4 w-4 text-gray-500" />
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  {showRobotAnimation ? 'Robot Animation' : 'Nested Daswos App'} - Drag to move
                </span>
              </div>
              <div className="text-xs text-gray-500 dark:text-gray-400">
                Scale: {Math.round(scale * 100)}%
              </div>
            </div>
          )}

          {/* Iframe Content */}
          <iframe
            ref={iframeRef}
            src={showRobotAnimation ? "/robot-animation/index.html" : "/"}
            title={showRobotAnimation ? "Daswos Robot Animation" : "Nested Daswos App"}
            className="w-full h-full border-0"
            style={{
              height: isFullscreen ? '100%' : 'calc(100% - 40px)',
              pointerEvents: isDragging ? 'none' : 'auto'
            }}
            onLoad={() => {
              console.log('Iframe loaded:', showRobotAnimation ? 'Robot Animation' : 'Nested App');
            }}
            onError={(e) => {
              console.error('Failed to load content:', e);
            }}
          />
        </div>

        {/* Instructions */}
        <div className="absolute bottom-4 left-4 right-4 text-center text-gray-600 dark:text-gray-400 pointer-events-none">
          <div className="bg-white/90 dark:bg-gray-800/90 rounded-lg px-4 py-3 inline-block max-w-2xl">
            <p className="text-sm mb-2">
              {showRobotAnimation
                ? "🤖 Interact with the Daswos robot! Use controls to change animations and drag to move the window."
                : "🏠 This is a nested Daswos app! You can drag, scale, and interact with it independently."
              }
            </p>
            <div className="text-xs text-gray-500 dark:text-gray-400 border-t border-gray-300 dark:border-gray-600 pt-2">
              <strong>Keyboard shortcuts:</strong> Ctrl+R (Reset) • Ctrl+F (Fullscreen) • Ctrl+T (Toggle) • Ctrl+/- (Scale)
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RobotAnimation;
